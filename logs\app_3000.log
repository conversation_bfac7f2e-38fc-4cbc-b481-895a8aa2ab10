2025-06-11 00:38:36,419 INFO: 应用启动 - PID: 3000 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 00:38:42,999 ERROR: 从入库单生成凭证失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            INSERT INTO financial_vouchers
            (voucher_number, voucher_date, area_id, voucher_type, summary,
             total_amount, status, source_type, source_id, attachment_count,
             created_by, reviewed_by, reviewed_at, posted_by, posted_at, notes)
            OUTPUT inserted.id
            VALUES
            (?, ?, ?, ?, ?,
             ?, ?, ?, ?, ?,
             ?, ?, ?, ?, ?, ?)
        ]
[parameters: ('CYQSYZXPZ20250611001', datetime.date(2025, 6, 3), 42, '入库凭证', '入库单RK20250603125249', 68000.0, '已审核', '入库单', 93, 0, 34, 34, datetime.datetime(2025, 6, 11, 0, 38, 42), None, None, '自动生成自入库单RK20250603125249')]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in D:\StudentsCMSSP\app\routes\financial\vouchers.py:1262]
